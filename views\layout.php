<?php
/**
 * Main Layout
 *
 * This file contains the main layout for the application.
 */

// Get the current user
$currentUser = getCurrentUser();

// Get the current language
$currentLanguage = getCurrentLanguage();

// Get unread notifications count
$unreadNotificationsCount = countUnreadNotifications($currentUser['id']);
?>
<!DOCTYPE html>
<html lang="<?php echo $currentLanguage; ?>" dir="<?php echo $currentLanguage === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' : ''; ?><?php echo __('app_name'); ?></title>

    <!-- Bootstrap CSS -->
    <?php
    $localBootstrap = $currentLanguage === 'ar' ? 'assets/vendor/bootstrap/css/bootstrap.rtl.min.css' : 'assets/vendor/bootstrap/css/bootstrap.min.css';
    $cdnBootstrap = $currentLanguage === 'ar' ? 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css' : 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css';
    ?>
    <?php if (file_exists($localBootstrap)): ?>
        <link rel="stylesheet" href="<?php echo getBaseUrl(); ?>/<?php echo $localBootstrap; ?>">
    <?php else: ?>
        <link rel="stylesheet" href="<?php echo $cdnBootstrap; ?>">
    <?php endif; ?>

    <!-- Font Awesome -->
    <?php if (file_exists('assets/vendor/fontawesome/css/all.min.css')): ?>
        <link rel="stylesheet" href="<?php echo getBaseUrl(); ?>/assets/vendor/fontawesome/css/all.min.css">
    <?php else: ?>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <?php endif; ?>

    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo getBaseUrl(); ?>/assets/css/style.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="<?php echo getBaseUrl(); ?>/assets/css/dark-mode.css?v=<?php echo time(); ?>">
</head>
<body class="<?php echo $currentLanguage === 'ar' ? 'rtl' : 'ltr'; ?><?php echo isset($_COOKIE['dark_mode']) && $_COOKIE['dark_mode'] === 'true' ? ' dark-mode' : ''; ?>">
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3 sidebar-sticky">
                    <div class="text-center mb-4">
                        <h5 class="text-white"><?php echo __('app_name'); ?></h5>
                    </div>

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('dashboard.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/dashboard">
                                <?php echo __('dashboard'); ?>
                            </a>
                        </li>

                        <?php if (hasPermission('view_hospitals') || hasPermission('manage_hospitals')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('hospitals.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/hospitals">
                                <?php echo __('hospitals'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_departments') || hasPermission('manage_departments')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('departments.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/departments">
                                <?php echo __('departments'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_devices') || hasPermission('manage_devices')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('devices.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/devices">
                                <?php echo __('devices'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_maintenance') || hasPermission('manage_maintenance')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('maintenance.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/maintenance">
                                <?php echo __('maintenance'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_tickets') || hasPermission('manage_tickets') || hasPermission('create_tickets')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('tickets.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/tickets">
                                <?php echo __('tickets'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('view_reports')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('reports.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/reports">
                                <?php echo __('reports'); ?>
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php if (hasPermission('manage_users')): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('users.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/users">
                                <?php echo __('users'); ?>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>

                    <hr class="text-white-50">

                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('profile.php') ? 'active' : ''; ?>" href="<?php echo getBaseUrl(); ?>/profile">
                                <?php echo __('profile'); ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo getBaseUrl(); ?>/logout">
                                <?php echo __('logout'); ?>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 fade-in-up">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-4 pb-3 mb-4">
                    <div>
                        <h1 class="h2 text-gradient mb-1"><?php echo isset($pageTitle) ? $pageTitle : __('dashboard'); ?></h1>
                        <p class="text-muted mb-0"><?php echo isset($pageSubtitle) ? $pageSubtitle : __('manage_your_medical_devices'); ?></p>
                    </div>

                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <a href="<?php echo getBaseUrl(); ?>/notifications" class="btn btn-outline-primary position-relative enhanced-btn">
                                <i class="fas fa-bell"></i>
                                <span class="d-none d-md-inline ms-1">Notifications</span>
                                <?php if ($unreadNotificationsCount > 0): ?>
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger animate">
                                    <?php echo $unreadNotificationsCount; ?>
                                </span>
                                <?php endif; ?>
                            </a>

                            <div class="dropdown me-2">
                                <button class="btn btn-outline-primary dropdown-toggle enhanced-btn" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <span class="language-text"><?php echo $currentLanguage === 'ar' ? 'العربية' : 'English'; ?></span>
                                </button>
                                <ul class="dropdown-menu shadow-lg border-0" aria-labelledby="languageDropdown">
                                    <li><a class="dropdown-item <?php echo $currentLanguage === 'en' ? 'active' : ''; ?>" href="?lang=en">
                                        English
                                    </a></li>
                                    <li><a class="dropdown-item <?php echo $currentLanguage === 'ar' ? 'active' : ''; ?>" href="?lang=ar">
                                        العربية
                                    </a></li>
                                </ul>
                            </div>

                            <button class="btn btn-outline-primary dark-mode-toggle enhanced-btn" id="darkModeToggle" title="<?php echo __('toggle_dark_mode'); ?>">
                                <span class="mode-text"><?php echo isset($_COOKIE['dark_mode']) && $_COOKIE['dark_mode'] === 'true' ? __('light_mode') : __('dark_mode'); ?></span>
                            </button>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['flash_message'])): ?>
                    <div class="alert alert-<?php echo $_SESSION['flash_message']['type']; ?> alert-dismissible fade show shadow-sm border-0 slide-in-right" role="alert">
                        <div class="d-flex align-items-center">
                            <div><?php echo $_SESSION['flash_message']['message']; ?></div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    <?php unset($_SESSION['flash_message']); ?>
                <?php endif; ?>

                <?php
                // Include the content
                if (isset($content)) {
                    echo $content;
                }
                ?>
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <?php if (file_exists('assets/vendor/bootstrap/js/bootstrap.bundle.min.js')): ?>
        <script src="<?php echo getBaseUrl(); ?>/assets/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <?php else: ?>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <?php endif; ?>

    <!-- jQuery -->
    <?php if (file_exists('assets/vendor/jquery/jquery-3.6.0.min.js')): ?>
        <script src="<?php echo getBaseUrl(); ?>/assets/vendor/jquery/jquery-3.6.0.min.js"></script>
    <?php else: ?>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <?php endif; ?>

    <!-- Enhanced Form Handling -->
    <script>
        // Enhanced form handling with success feedback
        document.addEventListener('DOMContentLoaded', function() {
            // Enhanced button interactions
            document.querySelectorAll('.btn').forEach(button => {
                button.addEventListener('click', function(e) {
                    if (this.type === 'submit' && !this.disabled) {
                        // Add loading state to submit buttons
                        const originalText = this.innerHTML;
                        this.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>Processing...';
                        this.disabled = true;

                        // Re-enable after 5 seconds as fallback
                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.disabled = false;
                        }, 5000);
                    }
                });
            });

            // Form clearing function for successful operations
            window.clearFormAfterSuccess = function(formId) {
                const form = document.getElementById(formId);
                if (form) {
                    form.reset();

                    // Remove validation classes
                    form.querySelectorAll('.is-valid, .is-invalid').forEach(el => {
                        el.classList.remove('is-valid', 'is-invalid');
                    });

                    // Clear any stored draft data
                    const formName = form.getAttribute('data-form-name') || formId;
                    localStorage.removeItem(formName + '_draft');

                    // Show success animation
                    form.style.transition = 'opacity 0.3s ease';
                    form.style.opacity = '0.7';
                    setTimeout(() => {
                        form.style.opacity = '1';
                    }, 300);
                }
            };

            // Auto-clear forms on success message
            <?php if (isset($_SESSION['flash_message']) && $_SESSION['flash_message']['type'] === 'success'): ?>
            setTimeout(() => {
                const forms = document.querySelectorAll('form[id$="Form"]');
                forms.forEach(form => {
                    if (form.id) {
                        clearFormAfterSuccess(form.id);
                    }
                });
            }, 100);
            <?php endif; ?>
        });

        // Function to get departments by hospital ID
        function getDepartmentsByHospital(hospitalId, departmentSelectId) {
            if (!hospitalId) {
                return;
            }

            $.ajax({
                url: '<?php echo getBaseUrl(); ?>/api/get_departments',
                type: 'GET',
                data: { hospital_id: hospitalId },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        var departments = response.data;
                        var departmentSelect = $('#' + departmentSelectId);

                        departmentSelect.empty();
                        departmentSelect.append('<option value=""><?php echo __('select_department'); ?></option>');

                        $.each(departments, function(index, department) {
                            departmentSelect.append('<option value="' + department.id + '">' + department.name + '</option>');
                        });
                    }
                }
            });
        }

        // Function to get devices by hospital ID and/or department ID
        function getDevicesByHospitalAndDepartment(hospitalId, departmentId, deviceSelectId) {
            $.ajax({
                url: '<?php echo getBaseUrl(); ?>/api/get_devices',
                type: 'GET',
                data: {
                    hospital_id: hospitalId,
                    department_id: departmentId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        var devices = response.data;
                        var deviceSelect = $('#' + deviceSelectId);

                        deviceSelect.empty();
                        deviceSelect.append('<option value=""><?php echo __('select_device'); ?></option>');

                        $.each(devices, function(index, device) {
                            deviceSelect.append('<option value="' + device.id + '">' + device.name + ' (' + device.serial_number + ')</option>');
                        });
                    }
                }
            });
        }

        // Function to get notifications
        function getNotifications() {
            $.ajax({
                url: '<?php echo getBaseUrl(); ?>/api/get_notifications',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        var count = response.data.count;
                        var notificationBadge = $('.notification-badge');

                        if (count > 0) {
                            notificationBadge.text(count).show();
                        } else {
                            notificationBadge.hide();
                        }
                    }
                }
            });
        }

        // Dark mode toggle
        $(document).ready(function() {
            // Toggle dark mode
            $('#darkModeToggle').click(function() {
                // Toggle body class
                $('body').toggleClass('dark-mode');

                // Update button text
                var isDarkMode = $('body').hasClass('dark-mode');
                var modeText = $(this).find('.mode-text');
                modeText.text(isDarkMode ? '<?php echo __('light_mode'); ?>' : '<?php echo __('dark_mode'); ?>');

                // Set cookie
                document.cookie = 'dark_mode=' + isDarkMode + '; path=/; max-age=31536000'; // 1 year
            });
        });

        // Check for new notifications every 60 seconds
        setInterval(getNotifications, 60000);
    </script>

    <?php if (isset($scripts)): ?>
        <?php echo $scripts; ?>
    <?php endif; ?>
</body>
</html>
