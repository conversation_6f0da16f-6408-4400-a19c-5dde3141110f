<?php
/**
 * Departments Controller
 * 
 * This file handles department-related operations.
 */

// Check if the user is logged in
requireLogin();

// Get the current user
$currentUser = getCurrentUser();
$userHospitalId = $currentUser['hospital_id'];

// Handle actions
$action = isset($url[1]) ? $url[1] : 'index';
$departmentId = isset($url[2]) ? (int)$url[2] : 0;

switch ($action) {
    case 'index':
        // Check if the user has permission to view departments
        if (!hasPermission('view_departments') && !hasPermission('manage_departments')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get all departments
        if ($userHospitalId && !hasPermission('manage_departments')) {
            // Non-admin users with hospital assignment can only see departments in their hospital
            $departments = $departmentModel->getByHospital($userHospitalId);
        } else {
            // Admin can see all departments
            $departments = $departmentModel->getAll();
        }
        
        // Get hospitals for the dropdown
        $hospitals = $hospitalModel->getAll();
        
        // Include the departments index view
        include 'views/departments/index.php';
        break;
        
    case 'create':
        // Check if the user has permission to manage departments
        requirePermission('manage_departments');
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
                setFlashMessage('error', __('invalid_csrf_token'));
                redirect('departments/create');
            }

            $departmentData = [
                'hospital_id' => $_POST['hospital_id'] ?? $userHospitalId,
                'name' => $_POST['name'] ?? '',
                'location' => $_POST['location'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'email' => $_POST['email'] ?? '',
                'notes' => $_POST['notes'] ?? ''
            ];

            // Validate data
            $errors = [];

            if (empty($departmentData['hospital_id'])) {
                $errors['hospital_id'] = __('required_field');
            }

            if (empty($departmentData['name'])) {
                $errors['name'] = __('required_field');
            }

            if (empty($departmentData['location'])) {
                $errors['location'] = __('required_field');
            }

            // Validate email format if provided
            if (!empty($departmentData['email']) && !filter_var($departmentData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            }
            
            // If no errors, create the department
            if (empty($errors)) {
                $departmentId = $departmentModel->create($departmentData);
                
                if ($departmentId) {
                    // Log the action
                    logAction('create_department', 'Created department: ' . $departmentData['name']);
                    
                    // Set flash message
                    setFlashMessage('success', __('created_successfully', [__('department')]));
                    
                    // Redirect to departments index
                    redirect('departments');
                } else {
                    $errors['general'] = __('create_failed');
                }
            }
        } else {
            // Initialize empty department data
            $departmentData = [
                'hospital_id' => $userHospitalId,
                'name' => '',
                'location' => '',
                'phone' => '',
                'email' => '',
                'notes' => ''
            ];

            $errors = [];
        }
        
        // Get hospitals for the dropdown
        if ($userHospitalId && !hasPermission('manage_hospitals')) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = $hospitalModel->getAll();
        }
        
        // Include the departments create view
        include 'views/departments/create.php';
        break;

    case 'store':
        // Check if the user has permission to manage departments
        requirePermission('manage_departments');

        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
                setFlashMessage('error', __('invalid_csrf_token'));
                redirect('departments/create');
            }

            $departmentData = [
                'hospital_id' => $_POST['hospital_id'] ?? $userHospitalId,
                'name' => $_POST['name'] ?? '',
                'location' => $_POST['location'] ?? '',
                'phone' => $_POST['phone'] ?? '',
                'email' => $_POST['email'] ?? '',
                'notes' => $_POST['notes'] ?? ''
            ];

            // Validate data
            $errors = [];

            if (empty($departmentData['hospital_id'])) {
                $errors['hospital_id'] = __('required_field');
            }

            if (empty($departmentData['name'])) {
                $errors['name'] = __('required_field');
            }

            if (empty($departmentData['location'])) {
                $errors['location'] = __('required_field');
            }

            // Validate email format if provided
            if (!empty($departmentData['email']) && !filter_var($departmentData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            }

            // If no errors, create the department
            if (empty($errors)) {
                $departmentId = $departmentModel->create($departmentData);

                if ($departmentId) {
                    // Log the action
                    logAction('create_department', 'Created department: ' . $departmentData['name']);

                    // Clear any stored form data
                    unset($_SESSION['form_data']);

                    // Set success message and redirect
                    setFlashMessage('success', __('department') . ' "' . $departmentData['name'] . '" ' . __('created_successfully'));
                    redirect('departments');
                } else {
                    $errors['general'] = __('create_failed');
                }
            }

            // If there are errors, redirect back to create form with errors
            if (!empty($errors)) {
                $_SESSION['form_errors'] = $errors;
                $_SESSION['form_data'] = $departmentData;
                redirect('departments/create');
            }
        } else {
            // If not POST request, redirect to create form
            redirect('departments/create');
        }
        break;

    case 'edit':
        // Check if the user has permission to manage departments
        requirePermission('manage_departments');
        
        // Get the department
        $department = $departmentModel->getById($departmentId);
        
        // Check if the department exists
        if (!$department) {
            setFlashMessage('error', __('not_found'));
            redirect('departments');
        }
        
        // Check if the user has permission to edit this department
        if ($userHospitalId && $department['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('departments');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Validate CSRF token
            if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
                setFlashMessage('error', __('invalid_csrf_token'));
                redirect('departments/edit/' . $departmentId);
            }

            $departmentData = [
                'hospital_id' => $_POST['hospital_id'] ?? $department['hospital_id'],
                'name' => $_POST['name'] ?? $department['name'],
                'location' => $_POST['location'] ?? $department['location'],
                'phone' => $_POST['phone'] ?? $department['phone'],
                'email' => $_POST['email'] ?? $department['email'],
                'notes' => $_POST['notes'] ?? $department['notes']
            ];

            // Validate data
            $errors = [];

            if (empty($departmentData['hospital_id'])) {
                $errors['hospital_id'] = __('required_field');
            }

            if (empty($departmentData['name'])) {
                $errors['name'] = __('required_field');
            }

            if (empty($departmentData['location'])) {
                $errors['location'] = __('required_field');
            }

            // Validate email format if provided
            if (!empty($departmentData['email']) && !filter_var($departmentData['email'], FILTER_VALIDATE_EMAIL)) {
                $errors['email'] = __('invalid_email');
            }
            
            // If no errors, update the department
            if (empty($errors)) {
                $result = $departmentModel->update($departmentId, $departmentData);
                
                if ($result) {
                    // Log the action
                    logAction('update_department', 'Updated department: ' . $departmentData['name']);
                    
                    // Set flash message
                    setFlashMessage('success', __('updated_successfully', [__('department')]));
                    
                    // Redirect to departments index
                    redirect('departments');
                } else {
                    $errors['general'] = __('update_failed');
                }
            }
        } else {
            $departmentData = $department;
            $errors = [];
        }
        
        // Get hospitals for the dropdown
        if ($userHospitalId && !hasPermission('manage_hospitals')) {
            $hospitals = [$hospitalModel->getById($userHospitalId)];
        } else {
            $hospitals = $hospitalModel->getAll();
        }
        
        // Include the departments edit view
        include 'views/departments/edit.php';
        break;
        
    case 'delete':
        // Check if the user has permission to manage departments
        requirePermission('manage_departments');
        
        // Get the department
        $department = $departmentModel->getById($departmentId);
        
        // Check if the department exists
        if (!$department) {
            setFlashMessage('error', __('not_found'));
            redirect('departments');
        }
        
        // Check if the user has permission to delete this department
        if ($userHospitalId && $department['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('departments');
        }
        
        // Handle form submission
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
            $result = $departmentModel->delete($departmentId);
            
            if ($result) {
                // Log the action
                logAction('delete_department', 'Deleted department: ' . $department['name']);
                
                // Set flash message
                setFlashMessage('success', __('deleted_successfully', [__('department')]));
            } else {
                setFlashMessage('error', __('delete_failed'));
            }
            
            // Redirect to departments index
            redirect('departments');
        }
        
        // Include the departments delete view
        include 'views/departments/delete.php';
        break;
        
    case 'view':
        // Check if the user has permission to view departments
        if (!hasPermission('view_departments') && !hasPermission('manage_departments')) {
            setFlashMessage('error', __('access_denied'));
            redirect('dashboard');
        }
        
        // Get the department
        $department = $departmentModel->getById($departmentId);
        
        // Check if the department exists
        if (!$department) {
            setFlashMessage('error', __('not_found'));
            redirect('departments');
        }
        
        // Check if the user has permission to view this department
        if ($userHospitalId && $department['hospital_id'] != $userHospitalId && !hasPermission('manage_hospitals')) {
            setFlashMessage('error', __('access_denied'));
            redirect('departments');
        }
        
        // Get department statistics
        $stats = $departmentModel->getStatistics($departmentId);
        
        // Get devices in this department
        $devices = $deviceModel->getAll(null, $departmentId);
        
        // Include the departments view
        include 'views/departments/view.php';
        break;
        
    default:
        // 404 Not Found
        header("HTTP/1.0 404 Not Found");
        include 'views/404.php';
        break;
}
