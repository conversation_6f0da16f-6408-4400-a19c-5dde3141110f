<?php
/**
 * Create Department View
 *
 * This file displays the form to create a new department.
 */

// Set page title
$pageTitle = __('add_department');

// Include header
include 'views/layout/header.php';

// Check for flash messages
$flashMessage = getFlashMessage();
if ($flashMessage) {
    echo '<div class="alert alert-' . $flashMessage['type'] . ' alert-dismissible fade show" role="alert">';
    echo $flashMessage['message'];
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('add_department'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('create_new_department'); ?></p>
    </div>

    <a href="<?php echo getBaseUrl(); ?>/departments" class="btn btn-outline-secondary">
        <?php echo __('back_to_list'); ?>
    </a>
</div>

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <?php echo __('department_information'); ?>
                </h5>
            </div>
            <div class="card-body">
        <form action="<?php echo getBaseUrl(); ?>/departments/store" method="post" id="departmentForm">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="name" class="form-label"><?php echo __('name'); ?> <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo isset($errors['name']) ? 'is-invalid' : ''; ?>"
                               id="name" name="name"
                               value="<?php echo htmlspecialchars($departmentData['name'] ?? ''); ?>" required>
                        <?php if (isset($errors['name'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['name']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="hospital_id" class="form-label"><?php echo __('hospital'); ?> <span class="text-danger">*</span></label>
                        <select class="form-select <?php echo isset($errors['hospital_id']) ? 'is-invalid' : ''; ?>"
                                id="hospital_id" name="hospital_id" required>
                            <option value=""><?php echo __('select_hospital'); ?></option>
                            <?php foreach ($hospitals as $hospital): ?>
                                <option value="<?php echo $hospital['id']; ?>"
                                    <?php echo (($departmentData['hospital_id'] ?? $_GET['hospital_id'] ?? '') == $hospital['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($hospital['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if (isset($errors['hospital_id'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['hospital_id']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <label for="location" class="form-label"><?php echo __('location'); ?> <span class="text-danger">*</span></label>
                <input type="text" class="form-control <?php echo isset($errors['location']) ? 'is-invalid' : ''; ?>"
                       id="location" name="location"
                       value="<?php echo htmlspecialchars($departmentData['location'] ?? ''); ?>" required>
                <?php if (isset($errors['location'])): ?>
                    <div class="invalid-feedback"><?php echo $errors['location']; ?></div>
                <?php endif; ?>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="phone" class="form-label"><?php echo __('phone'); ?></label>
                        <input type="tel" class="form-control <?php echo isset($errors['phone']) ? 'is-invalid' : ''; ?>"
                               id="phone" name="phone"
                               value="<?php echo htmlspecialchars($departmentData['phone'] ?? ''); ?>">
                        <?php if (isset($errors['phone'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['phone']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="email" class="form-label"><?php echo __('email'); ?></label>
                        <input type="email" class="form-control <?php echo isset($errors['email']) ? 'is-invalid' : ''; ?>"
                               id="email" name="email"
                               value="<?php echo htmlspecialchars($departmentData['email'] ?? ''); ?>">
                        <?php if (isset($errors['email'])): ?>
                            <div class="invalid-feedback"><?php echo $errors['email']; ?></div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="notes" class="form-label"><?php echo __('notes'); ?></label>
                <textarea class="form-control" id="notes" name="notes" rows="3"><?php echo htmlspecialchars($departmentData['notes'] ?? ''); ?></textarea>
            </div>

            <?php if (isset($errors['general'])): ?>
                <div class="alert alert-danger" role="alert">
                    <?php echo $errors['general']; ?>
                </div>
            <?php endif; ?>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="reset" class="btn btn-secondary me-md-2">
                    <?php echo __('reset'); ?>
                </button>
                <button type="submit" class="btn btn-primary">
                    <?php echo __('save'); ?>
                </button>
            </div>
        </form>
    </div>
</div>

<?php
// Add scripts
$scripts = '
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize form validation
        $("#departmentForm").validate({
            rules: {
                name: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                hospital_id: {
                    required: true
                },
                location: {
                    required: true,
                    minlength: 2,
                    maxlength: 100
                },
                phone: {
                    minlength: 5,
                    maxlength: 20
                },
                email: {
                    email: true
                }
            },
            messages: {
                name: {
                    required: "' . __('field_required') . '",
                    minlength: "' . __('min_length', ['length' => 2]) . '",
                    maxlength: "' . __('max_length', ['length' => 100]) . '"
                },
                hospital_id: {
                    required: "' . __('field_required') . '"
                },
                location: {
                    required: "' . __('field_required') . '",
                    minlength: "' . __('min_length', ['length' => 2]) . '",
                    maxlength: "' . __('max_length', ['length' => 100]) . '"
                },
                phone: {
                    minlength: "' . __('min_length', ['length' => 5]) . '",
                    maxlength: "' . __('max_length', ['length' => 20]) . '"
                },
                email: {
                    email: "' . __('invalid_email') . '"
                }
            },
            errorElement: "div",
            errorPlacement: function(error, element) {
                error.addClass("invalid-feedback");
                error.insertAfter(element);
            },
            highlight: function(element, errorClass, validClass) {
                $(element).addClass("is-invalid").removeClass("is-valid");
            },
            unhighlight: function(element, errorClass, validClass) {
                $(element).removeClass("is-invalid").addClass("is-valid");
            },
            submitHandler: function(form) {
                // Disable submit button to prevent double submission
                $("button[type=submit]").prop("disabled", true).html(\'<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' . __('saving') . '...\');
                form.submit();
            }
        });
    });
</script>
';

// Include footer
include 'views/layout/footer.php';
?>
