<?php
/**
 * Roles Delete View
 * 
 * This view displays the confirmation form to delete a role.
 */

// Set page title
$pageTitle = __('delete_role');

// Include header
include 'views/layout/header.php';

// Check for flash messages
$flashMessage = getFlashMessage();
if ($flashMessage) {
    echo '<div class="alert alert-' . $flashMessage['type'] . ' alert-dismissible fade show" role="alert">';
    echo $flashMessage['message'];
    echo '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
    echo '</div>';
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 text-gradient mb-1"><?php echo __('delete_role'); ?></h1>
        <p class="text-muted mb-0"><?php echo __('confirm_role_deletion'); ?></p>
    </div>

    <a href="<?php echo getBaseUrl(); ?>/roles" class="btn btn-outline-secondary">
        <?php echo __('back_to_list'); ?>
    </a>
</div>

<div class="row">
    <div class="col-lg-8 col-md-10 mx-auto">
        <div class="card fade-in-up">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <?php echo __('confirm_deletion'); ?>
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <strong><?php echo __('warning'); ?>:</strong> <?php echo __('delete_role_warning'); ?>
                </div>

                <div class="mb-4">
                    <h6><?php echo __('role_to_delete'); ?>:</h6>
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <strong><?php echo __('role_name'); ?>:</strong><br>
                                    <code><?php echo htmlspecialchars($role['name']); ?></code>
                                </div>
                                <div class="col-md-6">
                                    <strong><?php echo __('display_name'); ?>:</strong><br>
                                    <?php echo htmlspecialchars($role['display_name']); ?>
                                </div>
                            </div>
                            
                            <?php if (!empty($role['description'])): ?>
                            <div class="mt-3">
                                <strong><?php echo __('description'); ?>:</strong><br>
                                <?php echo nl2br(htmlspecialchars($role['description'])); ?>
                            </div>
                            <?php endif; ?>
                            
                            <div class="mt-3">
                                <strong><?php echo __('users_with_role'); ?>:</strong>
                                <span class="badge bg-info"><?php echo $role['user_count']; ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($role['user_count'] > 0): ?>
                    <div class="alert alert-danger" role="alert">
                        <strong><?php echo __('cannot_delete'); ?>:</strong> 
                        <?php echo __('role_has_users', ['count' => $role['user_count']]); ?>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="<?php echo getBaseUrl(); ?>/roles" class="btn btn-secondary">
                            <?php echo __('back_to_list'); ?>
                        </a>
                    </div>
                <?php else: ?>
                    <form action="<?php echo getBaseUrl(); ?>/roles/delete/<?php echo $role['id']; ?>" method="post" id="deleteForm">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        <input type="hidden" name="confirm" value="yes">
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                            <label class="form-check-label" for="confirmDelete">
                                <?php echo __('confirm_delete_role'); ?>
                            </label>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?php echo getBaseUrl(); ?>/roles" class="btn btn-secondary me-md-2">
                                <?php echo __('cancel'); ?>
                            </a>
                            <button type="submit" class="btn btn-danger" id="deleteButton" disabled>
                                <?php echo __('delete_role'); ?>
                            </button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Add scripts
$scripts = '
<script>
    $(document).ready(function() {
        // Enable/disable delete button based on confirmation checkbox
        $("#confirmDelete").change(function() {
            $("#deleteButton").prop("disabled", !this.checked);
        });
        
        // Add confirmation dialog
        $("#deleteForm").submit(function(e) {
            if (!confirm("' . __('are_you_sure_delete_role') . '")) {
                e.preventDefault();
                return false;
            }
            
            // Disable submit button to prevent double submission
            $("#deleteButton").prop("disabled", true).html(\'<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' . __('deleting') . '...\');
        });
    });
</script>
';

// Include footer
include 'views/layout/footer.php';
?>
